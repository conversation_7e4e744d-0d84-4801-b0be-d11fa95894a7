2025-07-17 14:54:17,530 p=64070 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:54:17,534 p=64070 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:54:17,604 p=64070 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:54:17,639 p=64070 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:54:17,651 p=64070 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:54:17,666 p=64070 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:54:17,666 p=64070 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:54:17,699 p=64070 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:54:17,700 p=64070 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:54:17,700 p=64070 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:54:18,145 p=64070 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:54:18,146 p=64070 u=daddasoft n=ansible | failed: [localhost -> vps5] (item={'key': 'pharmalien.dev', 'value': 'vps5'}) => {"ansible_loop_var": "item", "item": {"key": "pharmalien.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}
2025-07-17 14:54:18,554 p=64070 u=daddasoft n=ansible | failed: [localhost -> vps5] (item={'key': 'webfixgroupe.dev', 'value': 'vps5'}) => {"ansible_loop_var": "item", "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}
2025-07-17 14:54:18,554 p=64070 u=daddasoft n=ansible | fatal: [localhost -> {{ item.value }}]: UNREACHABLE! => {"changed": false, "msg": "All items completed", "results": [{"ansible_loop_var": "item", "item": {"key": "pharmalien.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}, {"ansible_loop_var": "item", "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}], "unreachable": true}
2025-07-17 14:54:18,555 p=64070 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:54:18,555 p=64070 u=daddasoft n=ansible | localhost                  : ok=3    changed=1    unreachable=1    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-17 14:54:46,408 p=64217 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:54:46,412 p=64217 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:54:46,480 p=64217 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:54:46,512 p=64217 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:54:46,523 p=64217 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:54:46,538 p=64217 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:54:46,538 p=64217 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:54:46,569 p=64217 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:54:46,569 p=64217 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:54:46,570 p=64217 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:54:47,003 p=64217 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:54:47,004 p=64217 u=daddasoft n=ansible | failed: [localhost -> vps5] (item={'key': 'pharmalien.dev', 'value': 'vps5'}) => {"ansible_loop_var": "item", "item": {"key": "pharmalien.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}
2025-07-17 14:54:47,410 p=64217 u=daddasoft n=ansible | failed: [localhost -> vps5] (item={'key': 'webfixgroupe.dev', 'value': 'vps5'}) => {"ansible_loop_var": "item", "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}
2025-07-17 14:54:47,412 p=64217 u=daddasoft n=ansible | fatal: [localhost -> {{ item.value }}]: UNREACHABLE! => {"changed": false, "msg": "All items completed", "results": [{"ansible_loop_var": "item", "item": {"key": "pharmalien.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}, {"ansible_loop_var": "item", "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}], "unreachable": true}
2025-07-17 14:54:47,413 p=64217 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:54:47,413 p=64217 u=daddasoft n=ansible | localhost                  : ok=3    changed=1    unreachable=1    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-17 14:55:04,263 p=64319 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:55:04,266 p=64319 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:55:04,334 p=64319 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:55:04,365 p=64319 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:55:04,376 p=64319 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:55:04,390 p=64319 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:55:04,391 p=64319 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:55:04,422 p=64319 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:55:04,423 p=64319 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:55:04,423 p=64319 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:55:09,149 p=64319 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:55:09,149 p=64319 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:55:11,532 p=64319 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:55:11,533 p=64319 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but future installation of another
Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 14:55:11,556 p=64319 u=daddasoft n=ansible | TASK [sync_produit : Wait for all async jobs to finish] ************************************************************************************************
2025-07-17 14:55:11,556 p=64319 u=daddasoft n=ansible | fatal: [localhost -> {{ item._ansible_item_result.delegate_to }}]: FAILED! => {"msg": "'dict object' has no attribute '_ansible_item_result'"}
2025-07-17 14:55:11,557 p=64319 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:55:11,557 p=64319 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 14:55:49,782 p=64572 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:55:49,785 p=64572 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:55:49,855 p=64572 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:55:49,887 p=64572 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:55:49,899 p=64572 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:55:49,914 p=64572 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:55:49,915 p=64572 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:55:49,949 p=64572 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:55:49,949 p=64572 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:55:49,949 p=64572 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:55:55,289 p=64572 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:55:55,289 p=64572 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:55:57,469 p=64572 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:55:57,471 p=64572 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but future installation of another
Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 14:55:57,494 p=64572 u=daddasoft n=ansible | TASK [sync_produit : Wait for all async jobs to finish] ************************************************************************************************
2025-07-17 14:55:57,494 p=64572 u=daddasoft n=ansible | An exception occurred during task execution. To see the full traceback, use -vvv. The error was: TypeError: unhashable type: 'dict'
2025-07-17 14:55:57,495 p=64572 u=daddasoft n=ansible | fatal: [localhost -> {{ item }}]: FAILED! => {"msg": "Unexpected failure during module execution: unhashable type: 'dict'", "stdout": ""}
2025-07-17 14:55:57,495 p=64572 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:55:57,495 p=64572 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 14:56:31,055 p=64774 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:56:31,059 p=64774 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:56:31,133 p=64774 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:56:31,165 p=64774 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:56:31,176 p=64774 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:56:31,192 p=64774 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:56:31,192 p=64774 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:56:31,226 p=64774 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:56:31,226 p=64774 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:56:31,226 p=64774 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:56:33,458 p=64774 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:56:33,458 p=64774 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:56:35,611 p=64774 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:56:35,612 p=64774 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but future installation of another
Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 14:56:35,804 p=64774 u=daddasoft n=ansible | TASK [sync_produit : Wait for all async jobs to finish] ************************************************************************************************
2025-07-17 14:56:35,805 p=64774 u=daddasoft n=ansible | failed: [localhost] (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j679483150211.1056462', 'results_file': '/home/<USER>/.ansible_async/j679483150211.1056462', 'changed': True, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {"ansible_job_id": "j679483150211.1056462", "ansible_loop_var": "item", "changed": false, "finished": 1, "item": {"ansible_job_id": "j679483150211.1056462", "ansible_loop_var": "item", "changed": true, "failed": 0, "finished": 0, "item": {"key": "pharmalien.dev", "value": "vps5"}, "results_file": "/home/<USER>/.ansible_async/j679483150211.1056462", "started": 1}, "msg": "could not find job", "results_file": "/home/<USER>/.ansible_async/j679483150211.1056462", "started": 1, "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 14:56:35,910 p=64774 u=daddasoft n=ansible | failed: [localhost] (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j840458782478.1056535', 'results_file': '/home/<USER>/.ansible_async/j840458782478.1056535', 'changed': True, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {"ansible_job_id": "j840458782478.1056535", "ansible_loop_var": "item", "changed": false, "finished": 1, "item": {"ansible_job_id": "j840458782478.1056535", "ansible_loop_var": "item", "changed": true, "failed": 0, "finished": 0, "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "results_file": "/home/<USER>/.ansible_async/j840458782478.1056535", "started": 1}, "msg": "could not find job", "results_file": "/home/<USER>/.ansible_async/j840458782478.1056535", "started": 1, "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 14:56:35,911 p=64774 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:56:35,911 p=64774 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 14:57:07,358 p=64988 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:57:07,361 p=64988 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:57:07,433 p=64988 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:57:07,464 p=64988 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:57:07,476 p=64988 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:57:07,492 p=64988 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:57:07,492 p=64988 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:57:07,526 p=64988 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:57:07,526 p=64988 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:57:07,526 p=64988 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:57:09,938 p=64988 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:57:09,938 p=64988 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:57:12,265 p=64988 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:57:12,266 p=64988 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but future installation of another
Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 14:57:12,471 p=64988 u=daddasoft n=ansible | TASK [sync_produit : Wait for all async jobs to finish] ************************************************************************************************
2025-07-17 14:57:12,471 p=64988 u=daddasoft n=ansible | failed: [localhost] (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j856306452467.1056972', 'results_file': '/home/<USER>/.ansible_async/j856306452467.1056972', 'changed': True, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {"ansible_job_id": "j856306452467.1056972", "ansible_loop_var": "item", "changed": false, "finished": 1, "item": {"ansible_job_id": "j856306452467.1056972", "ansible_loop_var": "item", "changed": true, "failed": 0, "finished": 0, "item": {"key": "pharmalien.dev", "value": "vps5"}, "results_file": "/home/<USER>/.ansible_async/j856306452467.1056972", "started": 1}, "msg": "could not find job", "results_file": "/home/<USER>/.ansible_async/j856306452467.1056972", "started": 1, "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 14:57:12,572 p=64988 u=daddasoft n=ansible | failed: [localhost] (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j152785213864.1057068', 'results_file': '/home/<USER>/.ansible_async/j152785213864.1057068', 'changed': True, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {"ansible_job_id": "j152785213864.1057068", "ansible_loop_var": "item", "changed": false, "finished": 1, "item": {"ansible_job_id": "j152785213864.1057068", "ansible_loop_var": "item", "changed": true, "failed": 0, "finished": 0, "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "results_file": "/home/<USER>/.ansible_async/j152785213864.1057068", "started": 1}, "msg": "could not find job", "results_file": "/home/<USER>/.ansible_async/j152785213864.1057068", "started": 1, "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 14:57:12,574 p=64988 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:57:12,574 p=64988 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 14:58:06,676 p=65271 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:58:06,680 p=65271 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:58:06,752 p=65271 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:58:06,785 p=65271 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:58:06,797 p=65271 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:58:06,813 p=65271 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:58:06,813 p=65271 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:58:06,851 p=65271 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:58:06,851 p=65271 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:58:06,852 p=65271 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:58:09,195 p=65271 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:58:09,195 p=65271 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:58:14,718 p=65271 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:58:14,720 p=65271 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but future installation of another
Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 14:58:14,744 p=65271 u=daddasoft n=ansible | TASK [sync_produit : Wait for all async jobs to finish] ************************************************************************************************
2025-07-17 14:58:14,744 p=65271 u=daddasoft n=ansible | fatal: [localhost -> {{ item._ansible_item_result.delegate_to }}]: FAILED! => {"msg": "'dict object' has no attribute '_ansible_item_result'"}
2025-07-17 14:58:14,745 p=65271 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:58:14,745 p=65271 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 15:02:30,510 p=66877 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:02:30,513 p=66877 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:02:30,585 p=66877 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:02:30,619 p=66877 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:02:30,630 p=66877 u=daddasoft n=ansible | ERROR! no module/action detected in task.

The error appears to be in '/home/<USER>/play-deomo/roles/sync_produit/tasks/main.yml': line 41, column 3, but may
be elsewhere in the file depending on the exact syntax problem.

The offending line appears to be:


- name :
  ^ here

2025-07-17 15:02:37,034 p=66962 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:02:37,037 p=66962 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:02:37,111 p=66962 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:02:37,141 p=66962 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:02:37,153 p=66962 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:02:37,169 p=66962 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************
2025-07-17 15:02:37,169 p=66962 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 15:02:37,207 p=66962 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:02:37,207 p=66962 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:02:37,208 p=66962 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:03:10,639 p=66962 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************
2025-07-17 15:03:10,639 p=66962 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:03:34,816 p=66962 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:03:34,817 p=66962 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:03:34,833 p=66962 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:03:34,833 p=66962 u=daddasoft n=ansible | fatal: [localhost]: FAILED! => {"msg": "'script_results' is undefined"}
2025-07-17 15:03:34,834 p=66962 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:03:34,834 p=66962 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 15:26:55,523 p=71096 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:26:55,530 p=71096 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:26:55,674 p=71096 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:26:55,728 p=71096 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:26:55,743 p=71096 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:26:55,761 p=71096 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************
2025-07-17 15:26:55,762 p=71096 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 15:26:55,809 p=71096 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:26:55,810 p=71096 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:26:55,810 p=71096 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:27:30,885 p=71096 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************
2025-07-17 15:27:30,885 p=71096 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:27:54,488 p=71096 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:27:54,489 p=71096 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:27:54,528 p=71096 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:27:54,528 p=71096 u=daddasoft n=ansible | ok: [localhost] => (item={'changed': True, 'stdout': 'receiving incremental file list\n\nsent 20 bytes  received 70 bytes  25.71 bytes/sec\ntotal size is 32,970,249  speedup is 366,336.10\nreceiving incremental file list\n\nsent 20 bytes  received 69 bytes  178.00 bytes/sec\ntotal size is 359,184  speedup is 4,035.78\nreceiving incremental file list\n\nsent 20 bytes  received 73 bytes  62.00 bytes/sec\ntotal size is 6,772,013  speedup is 72,817.34\nBEGIN\nTRUNCATE TABLE\nTRUNCATE TABLE\nCOPY 3540\nCOPY 153057\nUPDATE 3540\nINSERT 0 0\nUPDATE 153057\nINSERT 0 0\nUPDATE 51278\nUPDATE 14\nINSERT 0 0\nUPDATE 126\nINSERT 0 0\nUPDATE 0\nINSERT 0 0\nUPDATE 16\nINSERT 0 0\nINSERT 0 0\nUPDATE 153057\nDROP TABLE\nCREATE TABLE\nTRUNCATE TABLE\nCOPY 123904\nTRUNCATE TABLE\nINSERT 0 123904\nINSERT 0 6480\nCOMMIT', 'stderr': 'psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table "transco_produit_temp" does not exist, skipping', 'rc': 0, 'cmd': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', 'start': '2025-07-17 14:27:00.964671', 'end': '2025-07-17 14:27:32.313071', 'delta': '0:00:31.348400', 'msg': '', 'invocation': {'module_args': {'_raw_params': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', '_uses_shell': True, 'expand_argument_vars': True, 'stdin_add_newline': True, 'strip_empty_ends': True, 'argv': None, 'chdir': None, 'executable': None, 'creates': None, 'removes': None, 'stdin': None}}, 'stdout_lines': ['receiving incremental file list', '', 'sent 20 bytes  received 70 bytes  25.71 bytes/sec', 'total size is 32,970,249  speedup is 366,336.10', 'receiving incremental file list', '', 'sent 20 bytes  received 69 bytes  178.00 bytes/sec', 'total size is 359,184  speedup is 4,035.78', 'receiving incremental file list', '', 'sent 20 bytes  received 73 bytes  62.00 bytes/sec', 'total size is 6,772,013  speedup is 72,817.34', 'BEGIN', 'TRUNCATE TABLE', 'TRUNCATE TABLE', 'COPY 3540', 'COPY 153057', 'UPDATE 3540', 'INSERT 0 0', 'UPDATE 153057', 'INSERT 0 0', 'UPDATE 51278', 'UPDATE 14', 'INSERT 0 0', 'UPDATE 126', 'INSERT 0 0', 'UPDATE 0', 'INSERT 0 0', 'UPDATE 16', 'INSERT 0 0', 'INSERT 0 0', 'UPDATE 153057', 'DROP TABLE', 'CREATE TABLE', 'TRUNCATE TABLE', 'COPY 123904', 'TRUNCATE TABLE', 'INSERT 0 123904', 'INSERT 0 6480', 'COMMIT'], 'stderr_lines': ['psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table "transco_produit_temp" does not exist, skipping'], 'failed': False, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {
    "msg": "pharmalien.dev on vps5 ✅ SUCCESS"
}
2025-07-17 15:27:54,545 p=71096 u=daddasoft n=ansible | ok: [localhost] => (item={'changed': True, 'stdout': 'receiving incremental file list\n\nsent 20 bytes  received 70 bytes  60.00 bytes/sec\ntotal size is 32,970,249  speedup is 366,336.10\nreceiving incremental file list\n\nsent 20 bytes  received 69 bytes  59.33 bytes/sec\ntotal size is 359,184  speedup is 4,035.78\nreceiving incremental file list\n\nsent 20 bytes  received 73 bytes  62.00 bytes/sec\ntotal size is 6,772,013  speedup is 72,817.34\nBEGIN\nTRUNCATE TABLE\nCOPY 3540\nCOPY 153057\nUPDATE 3540\nINSERT 0 0\nUPDATE 153057\nINSERT 0 0\nUPDATE 51359\nDROP TABLE\nCREATE TABLE\nTRUNCATE TABLE\nCOPY 123904\nTRUNCATE TABLE\nINSERT 0 123904\nCOMMIT', 'stderr': 'psql:/db_dump/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sql:111: NOTICE:  table "transco_produit_temp" does not exist, skipping', 'rc': 0, 'cmd': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sh', 'start': '2025-07-17 14:27:33.382200', 'end': '2025-07-17 14:27:54.868788', 'delta': '0:00:21.486588', 'msg': '', 'invocation': {'module_args': {'_raw_params': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sh', '_uses_shell': True, 'expand_argument_vars': True, 'stdin_add_newline': True, 'strip_empty_ends': True, 'argv': None, 'chdir': None, 'executable': None, 'creates': None, 'removes': None, 'stdin': None}}, 'stdout_lines': ['receiving incremental file list', '', 'sent 20 bytes  received 70 bytes  60.00 bytes/sec', 'total size is 32,970,249  speedup is 366,336.10', 'receiving incremental file list', '', 'sent 20 bytes  received 69 bytes  59.33 bytes/sec', 'total size is 359,184  speedup is 4,035.78', 'receiving incremental file list', '', 'sent 20 bytes  received 73 bytes  62.00 bytes/sec', 'total size is 6,772,013  speedup is 72,817.34', 'BEGIN', 'TRUNCATE TABLE', 'COPY 3540', 'COPY 153057', 'UPDATE 3540', 'INSERT 0 0', 'UPDATE 153057', 'INSERT 0 0', 'UPDATE 51359', 'DROP TABLE', 'CREATE TABLE', 'TRUNCATE TABLE', 'COPY 123904', 'TRUNCATE TABLE', 'INSERT 0 123904', 'COMMIT'], 'stderr_lines': ['psql:/db_dump/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sql:111: NOTICE:  table "transco_produit_temp" does not exist, skipping'], 'failed': False, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {
    "msg": "webfixgroupe.dev on vps5 ✅ SUCCESS"
}
2025-07-17 15:27:54,553 p=71096 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:27:54,553 p=71096 u=daddasoft n=ansible | localhost                  : ok=5    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-17 15:29:22,896 p=71694 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:29:22,900 p=71694 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:29:22,969 p=71694 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:29:23,000 p=71694 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:29:23,012 p=71694 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:29:23,028 p=71694 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************
2025-07-17 15:29:23,029 p=71694 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 15:29:23,063 p=71694 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:29:23,063 p=71694 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:29:23,064 p=71694 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:29:54,278 p=71694 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************
2025-07-17 15:29:54,278 p=71694 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:29:55,378 p=71694 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:29:55,378 p=71694 u=daddasoft n=ansible | failed: [localhost -> vps5(**************)] (item={'key': 'webfixgroupe.dev', 'value': 'vps5'}) => {"ansible_loop_var": "item", "changed": true, "cmd": "exit 1", "delta": "0:00:00.005423", "end": "2025-07-17 14:29:57.164739", "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "msg": "non-zero return code", "rc": 1, "start": "2025-07-17 14:29:57.159316", "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 15:29:55,380 p=71694 u=daddasoft n=ansible | ...ignoring
2025-07-17 15:29:55,408 p=71694 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:29:55,411 p=71694 u=daddasoft n=ansible | ok: [localhost] => (item={'changed': True, 'stdout': 'receiving incremental file list\n\nsent 20 bytes  received 70 bytes  60.00 bytes/sec\ntotal size is 32,970,249  speedup is 366,336.10\nreceiving incremental file list\n\nsent 20 bytes  received 69 bytes  59.33 bytes/sec\ntotal size is 359,184  speedup is 4,035.78\nreceiving incremental file list\n\nsent 20 bytes  received 73 bytes  62.00 bytes/sec\ntotal size is 6,772,013  speedup is 72,817.34\nBEGIN\nTRUNCATE TABLE\nTRUNCATE TABLE\nCOPY 3540\nCOPY 153057\nUPDATE 3540\nINSERT 0 0\nUPDATE 153057\nINSERT 0 0\nUPDATE 51278\nUPDATE 14\nINSERT 0 0\nUPDATE 126\nINSERT 0 0\nUPDATE 0\nINSERT 0 0\nUPDATE 16\nINSERT 0 0\nINSERT 0 0\nUPDATE 153057\nDROP TABLE\nCREATE TABLE\nTRUNCATE TABLE\nCOPY 123904\nTRUNCATE TABLE\nINSERT 0 123904\nINSERT 0 6480\nCOMMIT', 'stderr': 'psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table "transco_produit_temp" does not exist, skipping', 'rc': 0, 'cmd': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', 'start': '2025-07-17 14:29:28.341841', 'end': '2025-07-17 14:29:55.961202', 'delta': '0:00:27.619361', 'msg': '', 'invocation': {'module_args': {'_raw_params': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', '_uses_shell': True, 'expand_argument_vars': True, 'stdin_add_newline': True, 'strip_empty_ends': True, 'argv': None, 'chdir': None, 'executable': None, 'creates': None, 'removes': None, 'stdin': None}}, 'stdout_lines': ['receiving incremental file list', '', 'sent 20 bytes  received 70 bytes  60.00 bytes/sec', 'total size is 32,970,249  speedup is 366,336.10', 'receiving incremental file list', '', 'sent 20 bytes  received 69 bytes  59.33 bytes/sec', 'total size is 359,184  speedup is 4,035.78', 'receiving incremental file list', '', 'sent 20 bytes  received 73 bytes  62.00 bytes/sec', 'total size is 6,772,013  speedup is 72,817.34', 'BEGIN', 'TRUNCATE TABLE', 'TRUNCATE TABLE', 'COPY 3540', 'COPY 153057', 'UPDATE 3540', 'INSERT 0 0', 'UPDATE 153057', 'INSERT 0 0', 'UPDATE 51278', 'UPDATE 14', 'INSERT 0 0', 'UPDATE 126', 'INSERT 0 0', 'UPDATE 0', 'INSERT 0 0', 'UPDATE 16', 'INSERT 0 0', 'INSERT 0 0', 'UPDATE 153057', 'DROP TABLE', 'CREATE TABLE', 'TRUNCATE TABLE', 'COPY 123904', 'TRUNCATE TABLE', 'INSERT 0 123904', 'INSERT 0 6480', 'COMMIT'], 'stderr_lines': ['psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table "transco_produit_temp" does not exist, skipping'], 'failed': False, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {
    "msg": "pharmalien.dev on vps5 ✅ SUCCESS"
}
2025-07-17 15:29:55,417 p=71694 u=daddasoft n=ansible | ok: [localhost] => (item={'changed': True, 'stdout': '', 'stderr': '', 'rc': 1, 'cmd': 'exit 1', 'start': '2025-07-17 14:29:57.159316', 'end': '2025-07-17 14:29:57.164739', 'delta': '0:00:00.005423', 'failed': True, 'msg': 'non-zero return code', 'invocation': {'module_args': {'_raw_params': 'exit 1', '_uses_shell': True, 'expand_argument_vars': True, 'stdin_add_newline': True, 'strip_empty_ends': True, 'argv': None, 'chdir': None, 'executable': None, 'creates': None, 'removes': None, 'stdin': None}}, 'stdout_lines': [], 'stderr_lines': [], 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {
    "msg": "webfixgroupe.dev on vps5 ❌ FAILED"
}
2025-07-17 15:29:55,424 p=71694 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:29:55,424 p=71694 u=daddasoft n=ansible | localhost                  : ok=5    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=1   
2025-07-17 15:31:37,353 p=72248 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:31:37,356 p=72248 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:31:37,427 p=72248 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:31:37,457 p=72248 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:31:37,469 p=72248 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:31:37,484 p=72248 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************
2025-07-17 15:31:37,485 p=72248 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 15:31:37,518 p=72248 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:31:37,518 p=72248 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:31:37,518 p=72248 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:32:16,280 p=72248 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************
2025-07-17 15:32:16,280 p=72248 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:32:43,807 p=72248 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:32:43,808 p=72248 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:32:43,838 p=72248 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:32:43,838 p=72248 u=daddasoft n=ansible | ok: [localhost] => (item=None)
2025-07-17 15:32:43,842 p=72248 u=daddasoft n=ansible | ok: [localhost] => (item=None)
2025-07-17 15:32:43,844 p=72248 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 15:32:43,850 p=72248 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:32:43,851 p=72248 u=daddasoft n=ansible | localhost                  : ok=5    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-17 15:33:10,824 p=72644 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:33:10,827 p=72644 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:33:10,911 p=72644 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:33:10,948 p=72644 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:33:10,961 p=72644 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:33:10,979 p=72644 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************
2025-07-17 15:33:10,980 p=72644 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 15:33:11,015 p=72644 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:33:11,016 p=72644 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:33:11,016 p=72644 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:33:44,850 p=72644 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************
2025-07-17 15:33:44,851 p=72644 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:34:10,132 p=72644 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:34:10,133 p=72644 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:34:10,153 p=72644 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:34:10,153 p=72644 u=daddasoft n=ansible | fatal: [localhost]: FAILED! => {"msg": "could not find 'results' key in iterated item {'changed': True, 'stdout': 'receiving incremental file list\\n\\nsent 20 bytes  received 70 bytes  25.71 bytes/sec\\ntotal size is 32,970,249  speedup is 366,336.10\\nreceiving incremental file list\\n\\nsent 20 bytes  received 69 bytes  59.33 bytes/sec\\ntotal size is 359,184  speedup is 4,035.78\\nreceiving incremental file list\\n\\nsent 20 bytes  received 73 bytes  62.00 bytes/sec\\ntotal size is 6,772,013  speedup is 72,817.34\\nBEGIN\\nTRUNCATE TABLE\\nTRUNCATE TABLE\\nCOPY 3540\\nCOPY 153057\\nUPDATE 3540\\nINSERT 0 0\\nUPDATE 153057\\nINSERT 0 0\\nUPDATE 51278\\nUPDATE 14\\nINSERT 0 0\\nUPDATE 126\\nINSERT 0 0\\nUPDATE 0\\nINSERT 0 0\\nUPDATE 16\\nINSERT 0 0\\nINSERT 0 0\\nUPDATE 153057\\nDROP TABLE\\nCREATE TABLE\\nTRUNCATE TABLE\\nCOPY 123904\\nTRUNCATE TABLE\\nINSERT 0 123904\\nINSERT 0 6480\\nCOMMIT', 'stderr': 'psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table \"transco_produit_temp\" does not exist, skipping', 'rc': 0, 'cmd': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', 'start': '2025-07-17 14:33:12.442940', 'end': '2025-07-17 14:33:45.085050', 'delta': '0:00:32.642110', 'msg': '', 'invocation': {'module_args': {'_raw_params': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', '_uses_shell': True, 'expand_argument_vars': True, 'stdin_add_newline': True, 'strip_empty_ends': True, 'argv': None, 'chdir': None, 'executable': None, 'creates': None, 'removes': None, 'stdin': None}}, 'stdout_lines': ['receiving incremental file list', '', 'sent 20 bytes  received 70 bytes  25.71 bytes/sec', 'total size is 32,970,249  speedup is 366,336.10', 'receiving incremental file list', '', 'sent 20 bytes  received 69 bytes  59.33 bytes/sec', 'total size is 359,184  speedup is 4,035.78', 'receiving incremental file list', '', 'sent 20 bytes  received 73 bytes  62.00 bytes/sec', 'total size is 6,772,013  speedup is 72,817.34', 'BEGIN', 'TRUNCATE TABLE', 'TRUNCATE TABLE', 'COPY 3540', 'COPY 153057', 'UPDATE 3540', 'INSERT 0 0', 'UPDATE 153057', 'INSERT 0 0', 'UPDATE 51278', 'UPDATE 14', 'INSERT 0 0', 'UPDATE 126', 'INSERT 0 0', 'UPDATE 0', 'INSERT 0 0', 'UPDATE 16', 'INSERT 0 0', 'INSERT 0 0', 'UPDATE 153057', 'DROP TABLE', 'CREATE TABLE', 'TRUNCATE TABLE', 'COPY 123904', 'TRUNCATE TABLE', 'INSERT 0 123904', 'INSERT 0 6480', 'COMMIT'], 'stderr_lines': ['psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table \"transco_produit_temp\" does not exist, skipping'], 'failed': False, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}"}
2025-07-17 15:34:10,154 p=72644 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:34:10,154 p=72644 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
