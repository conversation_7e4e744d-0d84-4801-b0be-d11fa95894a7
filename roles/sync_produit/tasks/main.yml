# Tasks for sync_produit



- name: test message
  ansible.builtin.debug:
    msg: Hello World!


- name: Register host with dynamic SSH password
  add_host:
    name: "{{ item.value }}"
    ansible_host: "{{vps_to_ip[item.value]}}"
    ansible_user: ubuntu
    ansible_ssh_pass: "{{ lookup('env', 'SSH_' + item.value | upper + '_PASS') }}"
    ansible_ssh_common_args: "-o Port=8700"
  loop: "{{ app_to_vps | dict2items }}"

- name: Execute script on each VPS with password
  ansible.builtin.shell: "{{ path_to_script[item.key] | default('echo \"Not Found\"') }}"
  delegate_to: "{{ item.value }}"
  loop: "{{ app_to_vps | dict2items }}"
  loop_control:
    loop_var: item
  when: path_to_script[item.key] is defined
  ignore_errors: yes
  register: script_results


- name: Show task result per app
  ansible.builtin.debug:
    msg: >-
      {{
        item.item.key ~ " on " ~ item.item.value ~
        (item.failed | default(false) | ternary(" ❌ FAILED", " ✅ SUCCESS"))
      }}
  loop: "{{ script_results.results }}"
  loop_control:
    label: "{{ item.item.key }}"
